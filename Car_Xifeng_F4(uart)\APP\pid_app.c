#include "pid_app.h"

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环



// 增量式PID：P-稳定性，I-响应性，D-准确性


PidParams_t pid_params_left = {
    .kp = 0.60f,
    .ki = 0.068f,
    .kd = 4.665f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_right = {
		.kp = 1.5f,
    .ki = 0.055f,
    .kd = 6.5f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};


void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);

  
  pid_set_target(&pid_speed_left, 30);
  pid_set_target(&pid_speed_right, 40);
}

// 低通滤波器系数 (Low-pass filter coefficient 'alpha')
// alpha 越小, 滤波效果越强, 但延迟越大。建议从 0.1 到 0.5 之间开始尝试。
#define SPEED_FILTER_ALPHA_LEFT  0.15f 
#define SPEED_FILTER_ALPHA_RIGHT 0.15f 

// 用于存储滤波后速度的变量
static float filtered_speed_left = 0.0f;
static float filtered_speed_right = 0.0f;


bool pid_running = true; // PID 控制使能开关

void PID_Task(void)
{
    if(pid_running == false) return;

    float output_left, output_right;
    
    // filtered = alpha * raw + (1 - alpha) * previous_filtered
    filtered_speed_left = SPEED_FILTER_ALPHA_LEFT * left_encoder.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_LEFT) * filtered_speed_left;
    filtered_speed_right = SPEED_FILTER_ALPHA_RIGHT * right_encoder.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_RIGHT) * filtered_speed_right;

    output_left = pid_calculate_incremental(&pid_speed_left, filtered_speed_left);
    output_right = pid_calculate_incremental(&pid_speed_right, filtered_speed_right);
		//output_yaw =  pid_calculate_incremental(&pid_yaw, yaw); -> PID计算 YAW -> 反馈
		//pid_set_target(&pid_speed_left,v + output_yaw); -> 作用对象
		//pid_set_target(&pid_speed_right,v - output_yaw);
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    // 设置电机速度
    Motor_SetSpeed(&left_motor, output_left);
    Motor_SetSpeed(&right_motor, output_right);
	
    my_printf(&huart1,"{left_filtered}%.2f,%.2f\r\n", pid_speed_left.target, filtered_speed_left);
		my_printf(&huart1,"{right_filtered}%.2f,%.2f\r\n", pid_speed_right.target, filtered_speed_right);
}


//void PID_Task(void)
//{
//  if(pid_running == false) return;
//  
//	float output_left, output_right;

//	// 使用增量式 PID 计算利用速度环计算输出
//	output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
//	output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);
//    
//	// 设置电机速度
//	Motor_SetSpeed(&left_motor, output_left);
////	Motor_SetSpeed(&right_motor, output_right);
////	my_printf(&huart1,"{left}%d,%d\r\n",(int)(pid_speed_left.target*100),(int)(left_encoder.speed_cm_s*100));
////	my_printf(&huart1,"{right}%d,%d\r\n",(int)(pid_speed_right.target*100),(int)(right_encoder.speed_cm_s*100));	
//	my_printf(&huart1,"{left}%.2f,%.2f\r\n",pid_speed_left.target,left_encoder.speed_cm_s);
////	my_printf(&huart1,"{right}%.2f,%.2f\r\n",pid_speed_right.target,right_encoder.speed_cm_s);
//}

