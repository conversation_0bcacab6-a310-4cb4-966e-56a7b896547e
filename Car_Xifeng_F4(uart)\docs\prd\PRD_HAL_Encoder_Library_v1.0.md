# HAL硬件编码器库产品需求文档 (PRD)

## 1. 文档信息

| 项目 | 信息 |
|------|------|
| 文档标题 | HAL硬件编码器库产品需求文档 |
| 版本号 | v1.0 |
| 创建日期 | 2025-01-04 |
| 负责人 | Emma (产品经理) |
| 项目名称 | Car_Xifeng_F4 HAL编码器库 |
| 目标平台 | STM32F407 + HAL库 |

## 2. 背景与问题陈述

### 2.1 项目背景
Car_Xifeng_F4项目是一个基于STM32F407的智能小车控制系统，目前已经实现了Motor电机驱动和HWT101陀螺仪传感器的组件库。为了实现精确的位置控制和速度反馈，需要添加编码器支持功能。

### 2.2 问题陈述
当前系统缺乏位置和速度反馈机制，无法实现：
- 精确的位置控制
- 速度闭环控制  
- 里程计算功能
- 运动状态监测

### 2.3 解决方案价值
通过实现HAL硬件编码器库，可以：
- 提供精确的位置和速度反馈
- 支持多种编码器类型（增量式、绝对式等）
- 保持与现有组件库的设计一致性
- 为上层控制算法提供可靠的数据基础

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **设计一致性目标**：创建与现有Motor和HWT101组件完全一致的编码器库
2. **功能完整性目标**：支持主流编码器类型和常用功能
3. **易用性目标**：提供简洁易用的应用层接口
4. **可靠性目标**：确保数据准确性和系统稳定性

### 3.2 关键结果 (Key Results)
1. **代码风格一致性**：100%遵循现有组件库的命名规范、结构设计、错误处理模式
2. **功能覆盖率**：支持增量式、绝对式、磁编码器、光电编码器4种类型
3. **接口完整性**：提供位置、速度、方向、状态等完整数据接口
4. **集成便利性**：与现有项目无缝集成，编译无错误

### 3.3 反向指标 (Counter Metrics)
1. **代码复杂度**：避免过度设计，保持代码简洁
2. **资源占用**：内存和CPU占用控制在合理范围内
3. **依赖复杂度**：最小化外部依赖，仅依赖HAL库

## 4. 用户画像与用户故事

### 4.1 目标用户
- **嵌入式开发工程师**：需要在STM32项目中集成编码器功能
- **控制算法工程师**：需要位置和速度反馈数据进行控制算法开发
- **系统集成工程师**：需要将编码器功能集成到完整的控制系统中

### 4.2 用户故事
1. **作为嵌入式开发工程师**，我希望能够快速集成编码器功能到现有项目中，无需修改现有代码架构
2. **作为控制算法工程师**，我希望获得准确的位置和速度数据，用于实现精确的运动控制
3. **作为系统集成工程师**，我希望编码器库与现有Motor和传感器库保持一致的接口风格

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 编码器类型支持
- **增量式编码器**：支持A/B相正交信号，可选Z相参考信号
- **绝对式编码器**：支持绝对位置读取
- **磁编码器**：支持磁性编码器
- **光电编码器**：支持光电式编码器

#### 5.1.2 数据获取功能
- **位置数据**：原始脉冲计数、毫米位置、圈数计数
- **速度数据**：脉冲/秒、转/分钟、毫米/秒
- **方向数据**：正转/反转/停止状态
- **状态数据**：运行状态、错误状态、数据有效性

#### 5.1.3 配置管理功能
- **硬件配置**：定时器通道、GPIO引脚、PPR设置
- **转换系数**：毫米每脉冲转换系数配置
- **位置复位**：位置计数器清零功能
- **校准功能**：支持Z脉冲校准（如果硬件支持）

### 5.2 接口设计

#### 5.2.1 数据结构设计
```c
// 编码器类型枚举
typedef enum {
    ENCODER_TYPE_INCREMENTAL = 0,
    ENCODER_TYPE_ABSOLUTE,
    ENCODER_TYPE_MAGNETIC,
    ENCODER_TYPE_OPTICAL
} EncoderType_t;

// 编码器状态枚举
typedef enum {
    ENCODER_STATE_IDLE = 0,
    ENCODER_STATE_RUNNING,
    ENCODER_STATE_ERROR,
    ENCODER_STATE_CALIBRATING
} EncoderState_t;

// 硬件配置结构体
typedef struct {
    TIM_HandleTypeDef* htim;
    uint32_t tim_channel_a;
    uint32_t tim_channel_b;
    GPIO_TypeDef* z_port;
    uint16_t z_pin;
    uint32_t ppr;
    uint8_t reverse;
    uint32_t max_rpm;
} EncoderHW_t;

// 数据结构体
typedef struct {
    int32_t position_raw;
    int32_t position_mm;
    float velocity_rpm;
    float velocity_mm_s;
    int8_t direction;
    uint32_t revolution_count;
    uint32_t timestamp;
    uint8_t data_valid;
    uint8_t z_pulse_detected;
} EncoderData_t;

// 主实体结构体
typedef struct {
    EncoderHW_t hw;
    EncoderData_t data;
    EncoderType_t type;
    EncoderState_t state;
    uint8_t enable;
    float mm_per_pulse;
    int32_t last_position;
    uint32_t last_timestamp;
} Encoder_t;
```

#### 5.2.2 核心API接口
```c
// 创建和初始化
int8_t Encoder_Create(Encoder_t* encoder, TIM_HandleTypeDef* htim, 
                      uint32_t channel_a, uint32_t channel_b,
                      uint32_t ppr, uint8_t reverse);

// 使能控制
int8_t Encoder_Enable(Encoder_t* encoder, uint8_t enable);

// 数据获取
int32_t Encoder_GetPosition(Encoder_t* encoder);
float Encoder_GetVelocity(Encoder_t* encoder);
int8_t Encoder_GetDirection(Encoder_t* encoder);
EncoderData_t* Encoder_GetData(Encoder_t* encoder);

// 配置管理
int8_t Encoder_ResetPosition(Encoder_t* encoder);
int8_t Encoder_SetConversionFactor(Encoder_t* encoder, float mm_per_pulse);

// 状态管理
EncoderState_t Encoder_GetState(Encoder_t* encoder);
int8_t Encoder_Update(Encoder_t* encoder);
```

### 5.3 业务逻辑规则

#### 5.3.1 数据更新规则
1. 编码器数据通过定时器硬件自动计数
2. 速度计算基于位置变化和时间差
3. 方向判断基于位置变化的正负
4. 数据有效性基于使能状态和硬件状态

#### 5.3.2 错误处理规则
1. 所有函数进行参数有效性检查
2. 失败时返回安全的默认值
3. 错误状态通过状态枚举管理
4. 数据无效时清除有效标志

### 5.4 边缘情况与异常处理

#### 5.4.1 硬件异常处理
- **定时器故障**：检测定时器状态，设置错误状态
- **信号丢失**：检测信号连续性，标记数据无效
- **计数溢出**：处理32位计数器溢出情况

#### 5.4.2 软件异常处理
- **NULL指针**：所有函数进行NULL指针检查
- **参数越界**：检查PPR、转换系数等参数范围
- **状态冲突**：确保状态转换的合法性

## 6. 范围定义

### 6.1 包含功能 (In Scope)
1. ✅ 增量式编码器支持（A/B相，可选Z相）
2. ✅ 绝对式编码器基础支持
3. ✅ 位置、速度、方向数据获取
4. ✅ 硬件定时器编码器模式配置
5. ✅ 多实例支持
6. ✅ 应用层封装接口
7. ✅ 完整的错误处理机制
8. ✅ 与现有组件库风格一致

### 6.2 排除功能 (Out of Scope)
1. ❌ 复杂的绝对式编码器协议（如SSI、BiSS）
2. ❌ 网络编码器支持（如EtherCAT编码器）
3. ❌ 高级滤波算法
4. ❌ 自动校准算法
5. ❌ 图形化配置工具
6. ❌ 实时操作系统集成

## 7. 依赖与风险

### 7.1 内部依赖项
- **STM32 HAL库**：依赖TIM和GPIO HAL驱动
- **现有组件库**：遵循Motor和HWT101的设计模式
- **项目构建系统**：需要更新包含路径和编译配置

### 7.2 外部依赖项
- **硬件平台**：STM32F407微控制器
- **开发环境**：Keil MDK-ARM开发环境
- **编码器硬件**：兼容的增量式或绝对式编码器

### 7.3 潜在风险
1. **硬件兼容性风险**：不同编码器的信号特性可能不同
   - 缓解措施：提供可配置的硬件参数
2. **性能风险**：高速编码器可能导致CPU负载过高
   - 缓解措施：使用硬件定时器，减少软件处理
3. **精度风险**：速度计算可能存在精度损失
   - 缓解措施：使用合适的数据类型和计算方法

## 8. 发布初步计划

### 8.1 开发阶段规划
1. **阶段1：基础架构**（预计2天）
   - 创建目录结构和基础文件
   - 定义数据结构和接口
   
2. **阶段2：核心功能**（预计3天）
   - 实现编码器创建和初始化
   - 实现数据获取和状态管理
   
3. **阶段3：完善功能**（预计2天）
   - 实现配置和控制功能
   - 创建应用层封装
   
4. **阶段4：集成测试**（预计1天）
   - 集成到项目构建系统
   - 编写文档和示例

### 8.2 测试策略
1. **单元测试**：测试各个函数的功能正确性
2. **集成测试**：测试与现有组件库的兼容性
3. **硬件测试**：使用实际编码器验证功能
4. **性能测试**：验证CPU和内存占用

### 8.3 发布标准
1. **功能完整性**：所有核心功能正常工作
2. **代码质量**：通过静态代码分析
3. **文档完整性**：API文档和使用示例完整
4. **集成验证**：与现有项目无缝集成

## 9. 附录

### 9.1 参考资料
- STM32F407参考手册
- STM32 HAL库文档
- 现有Motor和HWT101组件库源码

### 9.2 术语表
- **PPR**：Pulses Per Revolution，每转脉冲数
- **增量式编码器**：输出相对位置变化的编码器
- **绝对式编码器**：输出绝对位置的编码器
- **A/B相**：增量式编码器的两路正交信号
- **Z相**：编码器的参考位置信号

---

**文档状态**：已完成  
**下一步行动**：开始技术架构设计和开发任务分解